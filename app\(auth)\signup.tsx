import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  Image,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, { FadeInDown } from 'react-native-reanimated';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { useAuth } from '../../contexts/AuthContext';
import {
  validatePassword,
  getPasswordStrengthColor,
  getPasswordStrengthText,
} from '../../utils/passwordValidation';
import { getErrorDisplayMessage } from '../../utils/authErrorHandling';
import { useOptimizedAnimations } from '../../hooks/useOptimizedAnimations';

export default function SignupScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [passwordValidation, setPasswordValidation] = useState<{
    isValid: boolean;
    errors: string[];
    strength: 'weak' | 'medium' | 'strong';
  } | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const { signUp, signInWithGoogle, isLoading, clearError } = useAuth();

  // Clear auth errors when component mounts (user navigates to signup page)
  useEffect(() => {
    clearError();
  }, [clearError]);

  // Optimized animations for better performance
  const { headerAnimatedStyle, formAnimatedStyle, footerAnimatedStyle } =
    useOptimizedAnimations();

  // Handle password change with real-time validation
  const handlePasswordChange = (newPassword: string) => {
    setPassword(newPassword);
    if (newPassword.length > 0) {
      const validation = validatePassword(newPassword);
      setPasswordValidation(validation);
    } else {
      setPasswordValidation(null);
    }
  };

  const togglePasswordVisibility = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowConfirmPassword(!showConfirmPassword);
  };
  const handleSignUp = async () => {
    // Light haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setError(null); // Clear previous errors

    // Validation
    if (!email || !password || !confirmPassword) {
      setError('All fields are required.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    // Enhanced email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    // Password strength validation
    const passwordCheck = validatePassword(password);
    if (!passwordCheck.isValid) {
      setError(passwordCheck.errors[0]); // Show first error
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    if (!acceptedTerms) {
      setError(
        'Please accept the Terms of Service and Privacy Policy to continue.'
      );
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    try {
      await signUp(email, password);
      // Success haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation upon successful signup is handled by the AuthProvider's useEffect
      console.log('Signup successful');
    } catch (err: any) {
      console.error('Signup failed:', err);
      // Enhanced error handling with user-friendly messages
      const errorMessage = getErrorDisplayMessage(err.code, err.message);
      setError(errorMessage);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const handleGoogleSignUp = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setError(null); // Clear previous errors
    console.log('Google Sign-up attempt...');
    try {
      await signInWithGoogle();
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Google Sign-up failed:', err);
      const errorMessage = getErrorDisplayMessage(
        err.code || 'google/unknown-error',
        err.message
      );
      setError(errorMessage);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View className="flex-1 px-6 pt-12 pb-8">
          {/* Header Section */}
          <Animated.View
            className="items-center mb-12"
            style={headerAnimatedStyle}
          >
            {/* Logo */}
            <View className="mb-6">
              <Image
                source={require('@/assets/images/icon.png')}
                className="w-24 h-24"
                resizeMode="contain"
              />
            </View>

            {/* Welcome Text */}
            <Text className="mb-3 text-3xl font-bold text-center text-text-primary">
              Join Aril
            </Text>

            {/* Tagline */}
            <Text className="px-4 text-lg font-medium text-center text-text-secondary">
              Start your gift-giving journey
            </Text>
          </Animated.View>

          {/* Signup Form Container */}
          <View className="flex-1 justify-center">
            <Animated.View
              className="mx-auto w-full max-w-sm"
              style={formAnimatedStyle}
            >
              {/* Form Title */}
              <Text className="mb-8 text-2xl font-bold text-center text-text-primary">
                Create Account
              </Text>

              {/* Input Fields with improved spacing */}
              <View className="gap-6 mb-6">
                <Input
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  editable={!isLoading}
                  accessibilityLabel="Email address"
                  accessibilityHint="Enter your email address to create an account"
                  leftIcon={
                    <Ionicons
                      className="ml-2"
                      name="mail-outline"
                      size={20}
                      color="#7A3E4F"
                    />
                  }
                />
                <Input
                  placeholder="Password"
                  value={password}
                  onChangeText={handlePasswordChange}
                  secureTextEntry={!showPassword}
                  editable={!isLoading}
                  leftIcon={
                    <Ionicons
                      className="ml-2"
                      name="lock-closed-outline"
                      size={20}
                      color="#7A3E4F"
                    />
                  }
                  rightIcon={
                    <Ionicons
                      name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                      size={20}
                      color="#7A3E4F"
                      className="mr-2"
                    />
                  }
                  onRightIconPress={togglePasswordVisibility}
                />

                {/* Password Strength Indicator */}
                {passwordValidation && password.length > 0 && (
                  <View className="-mt-4 mb-2">
                    <View className="flex-row justify-between items-center mb-1">
                      <Text className="text-sm text-text-secondary">
                        Password Strength:
                      </Text>
                      <Text
                        className="text-sm font-medium"
                        style={{
                          color: getPasswordStrengthColor(
                            passwordValidation.strength
                          ),
                        }}
                      >
                        {getPasswordStrengthText(passwordValidation.strength)}
                      </Text>
                    </View>
                    {!passwordValidation.isValid && (
                      <View className="mt-1">
                        {passwordValidation.errors
                          .slice(0, 2)
                          .map((error, index) => (
                            <Text
                              key={index}
                              className="mb-1 text-xs text-feedback-error"
                            >
                              • {error}
                            </Text>
                          ))}
                      </View>
                    )}
                  </View>
                )}

                <Input
                  placeholder="Confirm Password"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  editable={!isLoading}
                  leftIcon={
                    <Ionicons
                      className="ml-2"
                      name="lock-closed-outline"
                      size={20}
                      color="#7A3E4F"
                    />
                  }
                  rightIcon={
                    <Ionicons
                      name={
                        showConfirmPassword ? 'eye-off-outline' : 'eye-outline'
                      }
                      size={20}
                      color="#7A3E4F"
                      className="mr-2"
                    />
                  }
                  onRightIconPress={toggleConfirmPasswordVisibility}
                />
              </View>

              {/* Display signup error message */}
              {error && (
                <Animated.View
                  className="p-4 mb-6 bg-red-50 rounded-xl border border-red-200"
                  entering={FadeInDown.duration(300)}
                >
                  <View className="flex-row items-center">
                    <Ionicons
                      name="alert-circle-outline"
                      size={20}
                      color="#D90429"
                      style={{ marginRight: 8 }}
                    />
                    <Text className="flex-1 text-sm text-feedback-error">
                      {error}
                    </Text>
                  </View>
                </Animated.View>
              )}

              {/* Terms Acceptance */}
              <View className="mb-6">
                <Pressable
                  onPress={() => setAcceptedTerms(!acceptedTerms)}
                  className="flex-row items-start"
                  disabled={isLoading}
                >
                  <View className="flex flex-row items-centerflex-1">
                    <View className="mr-3">
                      <Ionicons
                        name={
                          acceptedTerms ? 'checkbox-outline' : 'square-outline'
                        }
                        size={20}
                        color={acceptedTerms ? '#7A3E4F' : '#9CA3AF'}
                      />
                    </View>
                    <Text className="text-sm leading-5 text-text-secondary">
                      I agree to the{' '}
                      <Link href="/terms" asChild>
                        <Text className="underline text-primary-500">
                          Terms of Service
                        </Text>
                      </Link>{' '}
                      and{' '}
                      <Link href="/privacy" asChild>
                        <Text className="underline text-primary-500">
                          Privacy Policy
                        </Text>
                      </Link>
                    </Text>
                  </View>
                </Pressable>
              </View>

              {/* Signup Button */}
              <Button
                title="Create Account"
                onPress={handleSignUp}
                className="mb-6 rounded-xl"
                isLoading={isLoading}
                disabled={isLoading || !acceptedTerms}
                variant="primary"
                accessibilityLabel="Create account button"
                accessibilityHint="Tap to create your new account"
              />

              {/* Separator */}
              <View className="flex-row items-center my-6">
                <View className="flex-1 h-px bg-border" />
                <Text className="mx-4 text-sm font-medium text-text-secondary">
                  OR
                </Text>
                <View className="flex-1 h-px bg-border" />
              </View>

              {/* Google Sign-up Button */}
              <Button
                title="Continue with Google"
                onPress={handleGoogleSignUp}
                variant="secondary"
                className="mb-8 rounded-xl"
                isLoading={isLoading}
                disabled={isLoading}
                leftIcon={
                  <Ionicons name="logo-google" size={18} color="#FFFFFF" />
                }
              />
            </Animated.View>
          </View>

          {/* Footer Links */}
          <Animated.View className="pt-8 mt-auto" style={footerAnimatedStyle}>
            <Link href="/login" asChild>
              <Pressable disabled={isLoading} className="p-4">
                <Text className="text-base text-center text-text-secondary">
                  Already have an account?{' '}
                  <Text className="font-semibold underline text-primary-500">
                    Sign In
                  </Text>
                </Text>
              </Pressable>
            </Link>
          </Animated.View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
