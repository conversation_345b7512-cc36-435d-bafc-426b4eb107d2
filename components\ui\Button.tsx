import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
  Platform,
} from 'react-native';
import { useColorScheme } from 'nativewind';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary';
  isLoading?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  className?: string;
  textClassName?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button = React.forwardRef<TouchableOpacity, ButtonProps>(({
  title,
  onPress,
  variant = 'primary',
  isLoading = false,
  disabled = false,
  leftIcon,
  className = '',
  textClassName = '',
  style,
  textStyle,
  ...props
}, ref) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isDisabled = disabled || isLoading;

  // --- Base Classes ---
  // Consistent padding, rounding, flex properties with improved shadow
  const baseButtonClasses = 'py-3 px-5 rounded-lg flex-row justify-center items-center shadow-sm active:shadow-none overflow-hidden';
  // Base text styles - using font-medium for better readability
  const baseTextClasses = 'text-base font-medium text-center';

  // --- Variant Specific Classes ---
  let variantButtonClasses = '';
  let variantTextClasses = '';
  let loadingIndicatorColor = '#FFFFFF'; // Use white for loading indicator

  if (variant === 'primary') {
    // Primary Button Styles with improved active states
    variantButtonClasses = `
      bg-primary dark:bg-primary-dark
      ${!isDisabled ? 'active:bg-primary-600 dark:active:bg-primary-600' : ''}
    `;
    variantTextClasses = 'text-white';
    loadingIndicatorColor = '#FFFFFF';
  } else if (variant === 'secondary') {
    // Secondary Button Styles with improved active states
    variantButtonClasses = `
      bg-accent dark:bg-accent-dark
      ${!isDisabled ? 'active:bg-accent-600 dark:active:bg-accent-600' : ''}
    `;
    variantTextClasses = 'text-white';
    loadingIndicatorColor = '#FFFFFF';
  }

  // --- Disabled State ---
  // Improved disabled styling with lower opacity and subtle background change
  const disabledButtonClasses = isDisabled ? 'opacity-50' : '';

  // --- Combine Classes ---
  const combinedButtonClasses = `
    ${baseButtonClasses}
    ${variantButtonClasses}
    ${disabledButtonClasses}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const combinedTextClasses = `
    ${baseTextClasses}
    ${variantTextClasses}
    ${leftIcon ? 'ml-2' : ''}
    ${!isLoading && title ? '' : 'hidden'}
    ${textClassName}
  `.trim().replace(/\s+/g, ' ');

  return (
    <TouchableOpacity
      ref={ref}
      onPress={onPress}
      disabled={isDisabled}
      className={combinedButtonClasses}
      style={style}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityState={{ disabled: isDisabled, busy: isLoading }}
      accessibilityLabel={title}
      {...props}
    >
      {/* Loading Indicator with improved transition */}
      {isLoading && (
        <View className={`absolute inset-0 flex-row items-center justify-center ${variant === 'primary' ? 'bg-primary dark:bg-primary-dark' : 'bg-accent dark:bg-accent-dark'} rounded-lg`}>
          <ActivityIndicator
            size="small"
            color={loadingIndicatorColor}
          />
        </View>
      )}

              {/* Content with improved icon/text layout */}
        <View className={`flex-row items-center justify-center ${isLoading ? 'opacity-0' : 'opacity-100'}`}>
          {/* Left Icon with better spacing */}
          {leftIcon && (
            <View className={title ? 'mr-3' : ''}>
              {leftIcon}
            </View>
          )}

          {/* Text component */}
          {title ? (
            <Text 
              className={combinedTextClasses} 
              style={textStyle} 
              numberOfLines={1}
              adjustsFontSizeToFit={Platform.OS === 'ios'}
            >
              {title}
            </Text>
          ) : null}
        </View>
    </TouchableOpacity>
  );
});

Button.displayName = 'Button';

export default Button;