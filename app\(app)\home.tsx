// ~/app/(app)/home.tsx
import { AddGeneralNoteModal } from '../../components/profile/AddGeneralNoteModal';
import { v4 as uuidv4 } from 'uuid'; // LOW 6: Use uuid for temporary IDs
import { Timestamp } from 'firebase/firestore';
import { PastGiftGiven } from '../../types/firestore';
import { updateSignificantOther } from '../../services/profileService';
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Pressable,
  SafeAreaView,
  ScrollView,
  // Image, // Not used after removing commented-out logo
  TouchableOpacity,
  Alert, // For user feedback
} from 'react-native';
import {
  useFocusEffect,
  // useNavigation, // Not directly used in this snippet, router is used
  useRouter,
} from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  // FadeOut, // Not explicitly used in this snippet
  SlideInRight,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { Link } from 'expo-router';
import * as Haptics from 'expo-haptics';
import Button from '../../components/ui/Button';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import Card from '../../components/ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import { useProfileRedirect } from '../../hooks/useProfileRedirect';
import {
  fetchGiftRecommendations,
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry, // Assuming FeedbackEntry type is defined
  type GiftRecommendation, // Assuming GiftRecommendation type is defined
} from '../../services/recommendationService';
import { SignificantOtherProfile } from '../../functions/src/types/firestore'; // Adjust path if needed
import AsyncStorage from '@react-native-async-storage/async-storage';
import ActionMenu from '../../components/home/<USER>';
import MotivationalHeader from '../../components/home/<USER>';
import NavigationGrid from '../../components/home/<USER>';
import UpcomingDatesDisplay from '../../components/home/<USER>';
import AddCustomDateModal from '../../components/profile/AddCustomDateModal';
import EnhancedAddPastGiftModal from '../../components/profile/AddPastGiftModal';
import ProfileCompletionBanner from '../../components/home/<USER>';
import KeyDatesDisplay from '../../components/home/<USER>';
import GiftGallery from '../../components/home/<USER>';
import ProfileDropdown from '../../components/ui/ProfileDropdown';
import useCalendarData from '../../hooks/useCalendarData';
import { useRecommendationFeedback } from '../../hooks/useRecommendationFeedback';
import GiftContributionsChart from '../../components/charts/GiftContributionsChart';
import { useColorScheme } from 'nativewind'; // Or useThemeManager
import { 
  PROFILES_LAST_UPDATED_KEY,
  SELECTED_PROFILE_KEY,
  PROFILES_CACHE_KEY,
  CACHE_EXPIRY_KEY 
} from '../../constants/storageKeys';

const AnimatedCard = Animated.createAnimatedComponent(Card);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes

export default function HomeScreen() {
  // const navigation = useNavigation(); // Not used
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme(); // Using NativeWind's hook
  const isDark = colorScheme === 'dark';

  // Use automatic profile redirection
  useProfileRedirect();

  // Get calendar data and profiles from the hook instead of fetching separately
  const { upcomingDates, profiles, selectedProfileId: calendarSelectedProfileId, handleProfileSelect: calendarHandleProfileSelect, isLoading: calendarLoading } = useCalendarData();

  // Use profiles from useCalendarData instead of separate state
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(calendarSelectedProfileId);

  const {
    currentFeedbackMap,
    feedbackError,
    handleFeedback,
  } = useRecommendationFeedback(selectedProfileId);

  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isDateModalVisible, setIsDateModalVisible] = useState(false);
  const [isGiftModalVisible, setIsGiftModalVisible] = useState(false);

  const [loading, setLoading] = useState<boolean>(false); // Only for recommendations loading
  const [error, setError] = useState<string | null>(null); // Main data error

  const [recommendations, setRecommendations] = useState<
    GiftRecommendation[] | null
  >(null);
  const [recommendationsLoading, setRecommendationsLoading] =
    useState<boolean>(false);
  const [recommendationsError, setRecommendationsError] = useState<
    string | null
  >(null);

  const [dataInitialized, setDataInitialized] = useState(false);

  const headerOpacity = useSharedValue(0);
  const plusIconRotation = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);
  // const flatListRef = useRef(null); // Not used

  // Sync selectedProfileId with calendar hook
  useEffect(() => {
    if (calendarSelectedProfileId !== selectedProfileId) {
      setSelectedProfileId(calendarSelectedProfileId);
      // Clear recommendations when profile changes
      if (calendarSelectedProfileId !== selectedProfileId) {
        if (calendarSelectedProfileId) {
          fetchRecommendations(calendarSelectedProfileId, true); // Clear existing when switching profiles
        } else {
          setRecommendations(null);
        }
      }
    }
  }, [calendarSelectedProfileId, selectedProfileId]);

  // Initialize data when component mounts
  useEffect(() => {
    if (selectedProfileId && !dataInitialized) {
      fetchRecommendations(selectedProfileId, true); // Clear existing on initial load
      setDataInitialized(true);
      
      headerOpacity.value = withSequence(
        withTiming(0, { duration: 0 }),
        withTiming(1, { duration: 800 })
      );
    }
  }, [selectedProfileId, dataInitialized]);

  useEffect(() => {
    plusIconRotation.value = withTiming(isMenuVisible ? 45 : 0, {
      duration: 200,
    });
    backdropOpacity.value = withTiming(isMenuVisible ? 0.5 : 0, {
      duration: 200,
    });
  }, [isMenuVisible, plusIconRotation, backdropOpacity]);

  const fetchRecommendations = useCallback(async (profileId: string, clearExisting: boolean = false) => {
    if (!profileId) return;
    setRecommendationsLoading(true);
    setRecommendationsError(null);
    
    // Only clear existing recommendations on initial load or when explicitly requested
    if (clearExisting) {
      setRecommendations(null);
    }
    
    try {
      const result = await fetchGiftRecommendations(profileId);
      setRecommendations(result || []);
    } catch (err: any) {
      setRecommendationsError(
        'Failed to load recommendations. Please try again.'
      );
      // Only clear recommendations on error if we don't have existing ones
      if (clearExisting || !recommendations) {
        setRecommendations(null);
      }
    } finally {
      setRecommendationsLoading(false);
    }
  }, [recommendations]); // Added recommendations as dependency

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));
  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
    pointerEvents: isMenuVisible ? 'auto' : 'none',
  }));
  const plusIconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${plusIconRotation.value}deg` }],
  }));

  const handleProfileSelect = useCallback(
    async (profileId: string) => {
      // Use the calendar hook's profile selection
      await calendarHandleProfileSelect(profileId);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    },
    [calendarHandleProfileSelect]
  );

  const handleGeneratePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (selectedProfileId) fetchRecommendations(selectedProfileId, false); // Don't clear existing when regenerating
  };

  const handleNavigateToAddProfile = () => {
    router.push('/profiles/add');
    setIsMenuVisible(false);
  };
  const handleNavigateToEditProfile = () => {
    if (selectedProfileId) {
      router.push(`/profiles/${selectedProfileId}/edit`);
    } else {
      Alert.alert('No Profile Selected', 'Please select a profile to edit.'); // LOW 1: User feedback
    }
    setIsMenuVisible(false);
  };
  const handleOpenNoteModal = () => {
    setIsNoteModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenDateModal = () => {
    setIsDateModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenGiftModal = () => {
    setIsGiftModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleSaveNote = async (noteText: string) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save note. Ensure a profile is selected.');
      return;
    }
    try {
      const newNote = { id: uuidv4(), note: noteText, date: Timestamp.now() };
      const updatedNotes = [...(selectedProfile.generalNotes || []), newNote];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        generalNotes: updatedNotes,
      });

      setIsNoteModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save note. Please try again.');
    }
  };

  const handleSaveDate = async (dateData: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (
      !user?.uid ||
      !selectedProfileId ||
      !selectedProfile ||
      !dateData.date
    ) {
      Alert.alert(
        'Error',
        'Cannot save date. Ensure a profile is selected and date is provided.'
      );
      return;
    }
    try {
      const newDate = {
        id: uuidv4(),
        name: dateData.name,
        type: 'Custom',
        date: Timestamp.fromDate(dateData.date),
      };
      const updatedDates = [...(selectedProfile.customDates || []), newDate];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        customDates: updatedDates,
      });

      setIsDateModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save date. Please try again.');
    }
  };

  const handleSaveGift = async (giftData: Omit<PastGiftGiven, 'date'> & { date: Date | null }) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save gift. Ensure a profile is selected.');
      return;
    }
    try {
      const newGift: PastGiftGiven = {
        ...giftData,
        date: giftData.date
          ? Timestamp.fromDate(giftData.date)
          : Timestamp.now(),
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        version: 2, // Enhanced version
      };
      const updatedGifts = [...(selectedProfile.pastGiftsGiven || []), newGift];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        pastGiftsGiven: updatedGifts,
      });

      setIsGiftModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save gift. Please try again.');
    }
  };

  const selectedProfile = useMemo(
    () => profiles?.find((p) => p.profileId === selectedProfileId),
    [profiles, selectedProfileId]
  );

  // --- Render Logic ---
  if (calendarLoading) {
    // Show initial full screen loader while calendar data loads
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <Text className="mb-6 text-xl font-semibold text-primary dark:text-primary-dark">
            Loading your profiles...
          </Text>
          <LoadingIndicator
            color={isDark ? '#C70039' : '#A3002B'}
            size="large"
          />
        </View>
      </SafeAreaView>
    );
  }

  // Show loading state while profiles are being fetched or user is being redirected
  if (calendarLoading || !profiles || profiles.length === 0) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" />
          <Text className="mt-4 text-text-secondary dark:text-text-secondary-dark">
            {profiles?.length === 0 ? 'Setting up your profile...' : 'Loading your dashboard...'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="relative flex-1 px-5 py-6">
        <Animated.View
          style={backdropAnimatedStyle}
          className="absolute inset-0 z-20 bg-black"
          onTouchEnd={() => setIsMenuVisible(false)}
        />
        <View className="flex-row justify-between items-center mb-8">
          <Animated.View style={headerAnimatedStyle}>
            <Text className="text-2xl font-bold text-primary dark:text-primary-dark">
              Giftmi
            </Text>
          </Animated.View>
          {profiles.length === 1 && selectedProfile ? (
            <View className="p-2 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark">
              <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                {selectedProfile.name}
              </Text>
            </View>
          ) : (
            <View className="relative w-1/2">
              <ProfileDropdown
                profiles={profiles}
                selectedProfileId={selectedProfileId}
                onProfileSelect={handleProfileSelect}
                trigger={
                  <View className="flex-row justify-between items-center p-2 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Text className="text-text-secondary dark:text-text-secondary-dark">
                      Profile:{' '}
                    </Text>
                    <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                      {profiles.find(p => p.profileId === selectedProfileId)?.name || 'Select Profile'}
                    </Text>
                    <Feather
                      name="chevron-down"
                      size={20}
                      color={isDark ? '#C70039' : '#A3002B'}
                    />
                  </View>
                }
              />
            </View>
          )}
          <TouchableOpacity
            onPress={() => setIsMenuVisible((prev) => !prev)}
            testID="plus-icon-button"
          >
            <Animated.View style={plusIconAnimatedStyle}>
              <Feather
                name="plus"
                size={28}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {isMenuVisible && (
          <ActionMenu
            className="absolute right-1 top-20 z-40"
            onClose={() => setIsMenuVisible(false)}
            onAddProfile={handleNavigateToAddProfile}
            onAddNote={handleOpenNoteModal}
            onAddDate={handleOpenDateModal}
            onAddGift={handleOpenGiftModal}
            onEditProfile={handleNavigateToEditProfile}
          />
        )}

        {feedbackError && (
          <Animated.View
            entering={FadeIn.duration(300)}
            className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
          >
            <Text className="text-sm text-center text-error dark:text-error-dark">
              {feedbackError}
            </Text>
          </Animated.View>
        )}

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          
          <View className="px-0">
            
            <MotivationalHeader 
              userName={user?.displayName || undefined}
              upcomingDatesCount={upcomingDates.length}
              profileCompletionCount={profiles.filter(profile => {
                // Basic completion check - profile has name, basic info, and at least some preferences
                return profile.name && 
                       (profile.birthday || profile.anniversary || profile.relationship) &&
                       (profile.interests?.length > 0 || profile.dislikes?.length > 0 || profile.wishlistItems?.length > 0);
              }).length}
              totalProfiles={profiles.length}
              enableEnhancedMessaging={true}
              selectedProfileName={profiles.find(p => p.profileId === selectedProfileId)?.name}
            />

            
            <ProfileCompletionBanner profile={selectedProfile || null} />

            
            <NavigationGrid
              selectedProfileId={selectedProfileId}
              selectedProfile={selectedProfile}
              onOpenNoteModal={handleOpenNoteModal}
              onOpenDateModal={handleOpenDateModal}
              onOpenGiftModal={handleOpenGiftModal}
            />

            {/* Gift Contributions Chart */}
            {selectedProfile && (
              <GiftContributionsChart
                gifts={(selectedProfile.pastGiftsGiven || []) as import('../../types/firestore').PastGiftGiven[]}
                profileName={selectedProfile.name}
                onSquareTap={(dayData) => {
                  // Handle chart square tap - could show gift details or open modal
                  console.log('Chart square tapped:', dayData);
                }}
                onAddGiftPress={(date) => {
                  // Handle add gift from chart - open gift modal with pre-filled date
                  setIsGiftModalVisible(true);
                  console.log('Add gift for date:', date);
                }}
                className="mb-6"
              />
            )}

            
            <KeyDatesDisplay profile={selectedProfile || null} />

            
            {selectedProfileId && (
              <UpcomingDatesDisplay
                upcomingDates={upcomingDates.filter(
                  (date) =>
                    date.type !== 'Birthday' && date.type !== 'Anniversary'
                )}
                onAddDatePress={handleOpenDateModal}
              />
            )}
          </View>

          
          <View className="w-full">
            <GiftGallery
              recommendations={recommendations}
              isLoading={recommendationsLoading}
              error={recommendationsError}
              currentFeedbackMap={currentFeedbackMap}
              onFeedback={handleFeedback}
              onGenerateNewIdeas={handleGeneratePress}
              profileName={selectedProfile?.name}
              totalFeedback={currentFeedbackMap.size}
            />
          </View>
        </ScrollView>
      </View>
      <View>
        <AddGeneralNoteModal
          isVisible={isNoteModalVisible}
          onClose={() => setIsNoteModalVisible(false)}
          onSave={handleSaveNote}
        />
        <AddCustomDateModal
          isVisible={isDateModalVisible}
          onClose={() => setIsDateModalVisible(false)}
          onAddItem={handleSaveDate}
          profileId={selectedProfileId}
        />
        <EnhancedAddPastGiftModal
          isVisible={isGiftModalVisible}
          onClose={() => setIsGiftModalVisible(false)}
          onAddItem={handleSaveGift}
        />
      </View>
    </SafeAreaView>
  );
}
